{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.3", "donatj/phpuseragentparser": "^1.10", "filament/filament": "^4.0", "flowframe/laravel-trend": "^0.4.0", "laravel/framework": "^12.0", "laravel/horizon": "^5.33", "laravel/octane": "^2.11", "laravel/pulse": "^1.4", "laravel/tinker": "^2.10", "livewire/livewire": "^3.6", "maxmind-db/reader": "^1.12", "predis/predis": "^2.0", "spatie/laravel-activitylog": "^4.9", "spatie/laravel-permission": "^6.9"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.6", "fakerphp/faker": "^1.23", "filament/upgrade": "^4.0", "larastan/larastan": "^3.0", "laravel/dusk": "^8.3", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/sail": "^1.43", "laravel/telescope": "^5.10", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.8", "phpunit/phpunit": "^11.5"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve --host=0.0.0.0 --port=8000\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}