#!/bin/sh
set -e

cd /app

# Ensure storage directories exist with proper permissions
mkdir -p storage/logs storage/framework/cache storage/framework/sessions storage/framework/views storage/app storage/octane bootstrap/cache
chmod -R 775 storage bootstrap/cache
chown -R $(whoami):$(whoami) storage bootstrap/cache

# Run database migrations
php artisan migrate --force --seed --seeder=ProductionDatabaseSeeder

# Start FrankenPHP server
exec php artisan octane:start --server=frankenphp --workers=$OCTANE_WORKERS
