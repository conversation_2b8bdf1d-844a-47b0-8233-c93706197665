version: '3.8'

services:
    web:
        build:
            context: .
            dockerfile: deployment/octane.Dockerfile
            args:
                VERSION: ${VERSION:-dev}
                BUILD_DATE: ${BUILD_DATE}
                VCS_REF: ${VCS_REF}
        ports:
            - "8000:8000"
        volumes:
            - frankenphp-data:/tmp
            - storage-data:/app/storage
        env_file:
            - .env
        depends_on:
            postgres:
                condition: service_healthy
            redis:
                condition: service_healthy
        restart: unless-stopped
        networks:
            - linanok
        healthcheck:
            test: [ "CMD", "curl", "-f", "http://localhost:8000" ]
            interval: 5s
            timeout: 5s
            retries: 5

    queue-worker:
        build:
            context: .
            dockerfile: deployment/cli.Dockerfile
            args:
                VERSION: ${VERSION:-dev}
                BUILD_DATE: ${BUILD_DATE}
                VCS_REF: ${VCS_REF}
        command: [ "php", "artisan", "horizon" ]
        healthcheck:
            test: [ "CMD-SHELL", "php artisan horizon:status | grep -q 'running'" ]
            interval: 5s
            timeout: 5s
            retries: 5
        volumes:
            - storage-data:/app/storage
        env_file:
            - .env
        depends_on:
            postgres:
                condition: service_healthy
            redis:
                condition: service_healthy
        restart: unless-stopped
        networks:
            - linanok

    cli:
        build:
            context: .
            dockerfile: deployment/cli.Dockerfile
            args:
                VERSION: ${VERSION:-dev}
                BUILD_DATE: ${BUILD_DATE}
                VCS_REF: ${VCS_REF}
        volumes:
            - storage-data:/app/storage
        env_file:
            - .env
        depends_on:
            postgres:
                condition: service_healthy
            redis:
                condition: service_healthy
        networks:
            - linanok
        profiles:
            - cli

    init:
        build:
            context: .
            dockerfile: deployment/cli.Dockerfile
            args:
                VERSION: ${VERSION:-dev}
                BUILD_DATE: ${BUILD_DATE}
                VCS_REF: ${VCS_REF}
        entrypoint: [ "deployment/init.sh" ]
        volumes:
            - storage-data:/app/storage
        env_file:
            - .env
        depends_on:
            postgres:
                condition: service_healthy
            redis:
                condition: service_healthy
        networks:
            - linanok

    postgres:
        image: postgres:17-alpine
        volumes:
            - postgres-data:/var/lib/postgresql/data
        env_file:
            - .env
        environment:
            - POSTGRES_DB=${DB_DATABASE}
            - POSTGRES_USER=${DB_USERNAME}
            - POSTGRES_PASSWORD=${DB_PASSWORD}
        healthcheck:
            test: [ "CMD-SHELL", "pg_isready -U ${DB_USERNAME} -d ${DB_DATABASE}" ]
            interval: 5s
            timeout: 5s
            retries: 5
        networks:
            - linanok
        restart: unless-stopped

    redis:
        image: redis:8-alpine
        volumes:
            - redis-data:/data
        env_file:
            - .env
        command: redis-server --requirepass ${REDIS_PASSWORD}
        healthcheck:
            test: [ "CMD-SHELL", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping" ]
            interval: 5s
            timeout: 5s
            retries: 5
        networks:
            - linanok
        restart: unless-stopped

volumes:
    postgres-data:
    redis-data:
    frankenphp-data:
    storage-data:

networks:
    linanok:
        driver: bridge
